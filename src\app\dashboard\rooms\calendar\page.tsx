'use client';

import { useMemo } from 'react';
import { DateTime, Settings } from 'luxon';
import { BookingCalendarV2, BookingEvent, CreateBookingFormData } from '@/components/calendar';
import { useMeetingRooms } from '@/lib/hooks/use-meeting-rooms';
import { useBookings, useCreateBooking, useCancelBooking, CreateBookingInput } from '@/lib/hooks/use-bookings';
import './calendar.css';

// Configure <PERSON>xon for Spanish locale
Settings.defaultLocale = 'es';

export default function MeetingRoomsCalendarPage() {
  const { data: meetingRooms } = useMeetingRooms({
    limit: 100,
    isActive: true
  });

  // Fetch all bookings for calendar
  const { data: bookingsData } = useBookings({
    limit: 1000,
    resourceType: 'MEETING_ROOM'
  });

  const createBooking = useCreateBooking();
  const cancelBooking = useCancelBooking();

  // Transform bookings to calendar events
  const events: BookingEvent[] = useMemo(() => {
    return bookingsData?.data?.map((booking: any) => ({
      id: booking.id,
      title: booking.title,
      start: new Date(booking.startDate),
      end: new Date(booking.endDate),
      resource: booking.meetingRoom,
      booking: booking
    })) || [];
  }, [bookingsData]);

  // Handle booking creation
  const handleCreateBooking = async (data: CreateBookingFormData & { selectedDate?: Date }) => {
    if (!data.resourceId) {
      throw new Error('Selecciona una sala de reuniones');
    }

    // Use the selected date from the calendar slot
    const selectedDate = data.selectedDate ? DateTime.fromJSDate(data.selectedDate) : DateTime.now();
    const startDateTime = selectedDate.set({
      hour: parseInt(data.startTime.split(':')[0]),
      minute: parseInt(data.startTime.split(':')[1])
    });
    const endDateTime = selectedDate.set({
      hour: parseInt(data.endTime.split(':')[0]),
      minute: parseInt(data.endTime.split(':')[1])
    });

    const bookingData: CreateBookingInput = {
      resourceType: 'MEETING_ROOM',
      resourceId: data.resourceId,
      title: data.title,
      description: data.description,
      startDate: startDateTime.toISO() || '',
      endDate: endDateTime.toISO() || '',
      attendees: {
        internal: data.internalAttendees || [],
        external: data.externalAttendees || []
      }
    };

    await createBooking.mutateAsync(bookingData);
  };

  // Handle booking cancellation
  const handleCancelBooking = async (booking: any) => {
    await cancelBooking.mutateAsync({
      id: booking.id,
      reason: 'Cancelado por el usuario'
    });
  };

  return (
    <div className="space-y-6">
      <BookingCalendarV2
        events={events}
        meetingRooms={meetingRooms?.data || []}
        onCreateBooking={handleCreateBooking}
        onCancelBooking={handleCancelBooking}
        isCreatingBooking={createBooking.isPending}
        isCancellingBooking={cancelBooking.isPending}
      />
    </div>
  );
}
