'use client';

import { useState, useCallback } from 'react';
import { Calendar, luxonLocalizer, View } from 'react-big-calendar';
import { DateTime, Settings } from 'luxon';
import { toast } from 'sonner';
import { EventModal } from './EventModal';
import { CreateBookingModal } from './CreateBookingModal';
import { CancelBookingDialog } from './CancelBookingDialog';
import { useBookingValidation } from './hooks/useBookingValidation';
import { BookingEvent, SelectedSlot, CreateBookingFormData } from './types';
import 'react-big-calendar/lib/css/react-big-calendar.css';

// Configure Luxon for Spanish locale
Settings.defaultLocale = 'es';

const localizer = luxonLocalizer(DateTime, {
  firstDayOfWeek: 1 // Monday
});

interface BookingCalendarProps {
  events: BookingEvent[];
  meetingRooms: any[];
  onCreateBooking: (data: CreateBookingFormData) => Promise<void>;
  onCancelBooking: (booking: any) => Promise<void>;
  isCreatingBooking?: boolean;
  isCancellingBooking?: boolean;
  className?: string;
}

export function BookingCalendar({
  events,
  meetingRooms,
  onCreateBooking,
  onCancelBooking,
  isCreatingBooking = false,
  isCancellingBooking = false,
  className = ''
}: BookingCalendarProps) {
  const [currentView, setCurrentView] = useState<View>('week');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedSlot, setSelectedSlot] = useState<SelectedSlot | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<any>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEventModal, setShowEventModal] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [bookingToCancel, setBookingToCancel] = useState<any>(null);
  // State to keep the visual selection visible while modal is open
  const [visibleSelection, setVisibleSelection] = useState<any>(null);

  // Create events array with temporary selection event if exists
  const eventsWithSelection = visibleSelection
    ? [...events, {
      start: visibleSelection.start,
      end: visibleSelection.end,
      title: '',
      booking: null,
      id: 'temp-selection',
      isSelection: true
    }]
    : events;

  const { validateBookingRules, validateCapacity } = useBookingValidation();

  // Event style getter
  const eventStyleGetter = useCallback((event: any) => {
    // Handle temporary selection event
    if (event.isSelection) {
      return {
        style: {
          backgroundColor: 'rgba(59, 130, 246, 0.2)', // Light blue background
          borderRadius: '4px',
          border: '2px dashed #3b82f6', // Blue dashed border
          color: 'transparent', // Hide any text
          fontWeight: '500',
          boxShadow: '0 1px 3px rgba(59, 130, 246, 0.3)',
          padding: '4px 8px',
          lineHeight: '1.3',
          pointerEvents: 'none' as const // Prevent interaction with selection event
        }
      };
    }

    const booking = event.booking;
    let borderColor = '#6b7280'; // Default gray

    switch (booking?.status) {
      case 'CONFIRMED':
        borderColor = '#3b82f6'; // Blue
        break;
      case 'COMPLETED':
        borderColor = '#10b981'; // Green
        break;
      case 'CANCELLED':
        borderColor = '#ef4444'; // Red
        break;
      case 'NO_SHOW':
        borderColor = '#6b7280'; // Gray
        break;
    }

    return {
      style: {
        backgroundColor: '#f8f9fa', // Light gray background
        borderRadius: '4px',
        border: 'none',
        borderLeft: `4px solid ${borderColor}`, // Colored left border
        color: '#1f2937', // Dark gray text
        fontWeight: '500',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        padding: '4px 8px',
        lineHeight: '1.3'
        // Remove any positioning styles that interfere with react-big-calendar
      }
    };
  }, []);

  // Handle slot selection (clicking on empty time slots)
  const handleSelectSlot = useCallback((slotInfo: any) => {
    const now = new Date();
    const slotStart = new Date(slotInfo.start);

    // Don't allow booking in the past
    if (slotStart < now) {
      toast.error('No se pueden crear reservas en el pasado');
      return;
    }

    setSelectedSlot({
      start: slotInfo.start,
      end: slotInfo.end,
      resourceId: slotInfo.resourceId
    });

    // Keep the visual selection visible while modal is open
    // Create a temporary event object for the selection
    setVisibleSelection({
      start: slotInfo.start,
      end: slotInfo.end,
      title: 'Selección',
      resource: slotInfo.resourceId,
      id: 'temp-selection'
    });

    setShowCreateModal(true);
  }, []);

  // Handle event selection (clicking on existing events)
  const handleSelectEvent = useCallback((event: any) => {
    // Ignore clicks on the temporary selection event
    if (event.isSelection) {
      return;
    }

    // Clear any existing slot selection when clicking on an event
    setVisibleSelection(null);
    setSelectedSlot(null);
    setSelectedEvent(event.booking);
    setShowEventModal(true);
  }, []);

  // Handle view change
  const handleViewChange = useCallback((view: View) => {
    setCurrentView(view);
    // Clear selection when changing views
    setVisibleSelection(null);
    setSelectedSlot(null);
  }, []);

  // Handle navigation
  const handleNavigate = useCallback((date: Date) => {
    setCurrentDate(date);
    // Clear selection when navigating to different dates
    setVisibleSelection(null);
    setSelectedSlot(null);
  }, []);

  // Handle booking creation
  const handleCreateBooking = async (data: CreateBookingFormData) => {
    try {
      if (!selectedSlot) {
        throw new Error('No se ha seleccionado un horario');
      }

      // Validate booking rules
      const rulesValidation = validateBookingRules(data.startTime, data.endTime, selectedSlot.start);
      if (!rulesValidation.isValid) {
        toast.error(rulesValidation.error);
        return;
      }

      // Validate capacity
      const capacityValidation = validateCapacity();
      if (!capacityValidation.isValid) {
        toast.error(capacityValidation.error);
        return;
      }

      // Pass the selected date to the parent handler
      const dataWithDate = { ...data, selectedDate: selectedSlot.start };
      await onCreateBooking(dataWithDate);
      setShowCreateModal(false);
      setSelectedSlot(null);
      setVisibleSelection(null); // Clear visual selection when booking is created
      toast.success('Reserva creada exitosamente');
    } catch (error: any) {
      toast.error(error.message || 'Error al crear la reserva');
    }
  };

  // Handle booking cancellation
  const handleCancelBooking = (booking: any) => {
    setBookingToCancel(booking);
    setShowCancelDialog(true);
  };

  const confirmCancelBooking = async () => {
    if (!bookingToCancel) return;

    try {
      await onCancelBooking(bookingToCancel);
      setShowCancelDialog(false);
      setShowEventModal(false);
      setBookingToCancel(null);
      toast.success('Reserva cancelada exitosamente');
    } catch (error: any) {
      toast.error(error.message || 'Error al cancelar la reserva');
    }
  };

  // Check if booking can be cancelled
  const canCancelBooking = (booking: any) => {
    if (!booking) return false;
    const now = new Date();
    const bookingStart = new Date(booking.startDate);
    return booking.status === 'CONFIRMED' && bookingStart > now;
  };

  // Calendar messages in Spanish
  const messages = {
    allDay: 'Todo el día',
    previous: 'Anterior',
    next: 'Siguiente',
    today: 'Hoy',
    month: 'Mes',
    week: 'Semana',
    day: 'Día',
    agenda: 'Agenda',
    date: 'Fecha',
    time: 'Hora',
    event: 'Evento',
    noEventsInRange: 'No hay eventos en este rango',
    showMore: (total: number) => `+ ${total} más`
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            📅 Calendario de Reservas
          </h2>
          <p className="text-gray-600 text-sm mt-1">
            Haz clic en un evento para ver detalles o en un espacio vacío para crear una nueva reserva
          </p>
        </div>

        <div className="h-[600px]">
          <Calendar
            localizer={localizer}
            events={eventsWithSelection}
            startAccessor="start"
            endAccessor="end"
            titleAccessor="title"
            view={currentView}
            onView={handleViewChange}
            date={currentDate}
            onNavigate={handleNavigate}
            onSelectSlot={handleSelectSlot}
            onSelectEvent={handleSelectEvent}
            selectable
            popup
            eventPropGetter={eventStyleGetter}
            messages={messages}
            formats={{
              timeGutterFormat: 'HH:mm',
              eventTimeRangeFormat: ({ start, end }) => {
                const startTime = DateTime.fromJSDate(start).toFormat('HH:mm');
                const endTime = DateTime.fromJSDate(end).toFormat('HH:mm');
                return `${startTime} - ${endTime}`;
              },
              agendaTimeRangeFormat: ({ start, end }) => {
                const startTime = DateTime.fromJSDate(start).toFormat('HH:mm');
                const endTime = DateTime.fromJSDate(end).toFormat('HH:mm');
                return `${startTime} - ${endTime}`;
              }
            }}
            min={new Date(2024, 0, 1, 7, 0)} // 7:00 AM
            max={new Date(2024, 0, 1, 22, 0)} // 10:00 PM
            step={15}
            timeslots={4}
          />
        </div>
      </div>

      {/* Modals */}
      <CreateBookingModal
        isOpen={showCreateModal}
        onClose={() => {
          setShowCreateModal(false);
          setSelectedSlot(null);
          setVisibleSelection(null); // Clear visual selection when modal closes
        }}
        selectedSlot={selectedSlot}
        meetingRooms={meetingRooms}
        onSubmit={handleCreateBooking}
        isLoading={isCreatingBooking}
      />

      <EventModal
        isOpen={showEventModal}
        onClose={() => setShowEventModal(false)}
        booking={selectedEvent}
        onCancel={handleCancelBooking}
        canCancel={canCancelBooking(selectedEvent)}
      />

      <CancelBookingDialog
        isOpen={showCancelDialog}
        onClose={() => {
          setShowCancelDialog(false);
          setBookingToCancel(null);
        }}
        onConfirm={confirmCancelBooking}
        booking={bookingToCancel}
        isLoading={isCancellingBooking}
      />
    </div>
  );
}
