'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { DateTime } from 'luxon';
import { X, Calendar, Clock, Users, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { SelectedSlot, CreateBookingFormData } from './types';

interface CreateBookingPopoverProps {
  isOpen: boolean;
  onClose: () => void;
  selectedSlot: SelectedSlot | null;
  meetingRooms: any[];
  onSubmit: (data: CreateBookingFormData) => Promise<void>;
  isLoading?: boolean;
  position: { x: number; y: number };
}

export function CreateBookingPopover({
  isOpen,
  onClose,
  selectedSlot,
  meetingRooms,
  onSubmit,
  isLoading = false,
  position
}: CreateBookingPopoverProps) {
  const [selectedRoom, setSelectedRoom] = useState<any>(null);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors }
  } = useForm<CreateBookingFormData>();

  const watchedRoomId = watch('meetingRoomId');

  // Update selected room when room ID changes
  useEffect(() => {
    if (watchedRoomId) {
      const room = meetingRooms.find(r => r.id === watchedRoomId);
      setSelectedRoom(room);
    }
  }, [watchedRoomId, meetingRooms]);

  // Set default values when slot is selected
  useEffect(() => {
    if (selectedSlot && isOpen) {
      const startTime = DateTime.fromJSDate(selectedSlot.start).toFormat('HH:mm');
      const endTime = DateTime.fromJSDate(selectedSlot.end).toFormat('HH:mm');

      setValue('startTime', startTime);
      setValue('endTime', endTime);

      // Reset other fields
      setValue('title', '');
      setValue('description', '');
      setValue('attendees', 1);
      setValue('meetingRoomId', '');
      setSelectedRoom(null);
    }
  }, [selectedSlot, isOpen, setValue]);

  // Reset form when popover closes
  useEffect(() => {
    if (!isOpen) {
      reset();
      setSelectedRoom(null);
    }
  }, [isOpen, reset]);

  const handleFormSubmit = async (data: CreateBookingFormData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      // Error handling is done in parent component
    }
  };

  // Close popover when clicking outside
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      const popover = document.getElementById('booking-popover');

      // Don't close if clicking on calendar elements or the popover itself
      if (popover && !popover.contains(target)) {
        // Check if click is on calendar elements
        const isCalendarClick = target.closest('.rbc-calendar') ||
          target.closest('.rbc-event') ||
          target.closest('.rbc-time-slot');

        if (!isCalendarClick) {
          onClose();
        }
      }
    };

    // Add delay to prevent immediate closing when opening
    const timer = setTimeout(() => {
      document.addEventListener('mousedown', handleClickOutside);
    }, 200);

    return () => {
      clearTimeout(timer);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Close on Escape key
  useEffect(() => {
    if (!isOpen) return;

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  if (!isOpen || !selectedSlot) return null;

  const selectedDate = DateTime.fromJSDate(selectedSlot.start);
  const formattedDate = selectedDate.toFormat('EEEE, dd \'de\' MMMM \'de\' yyyy');

  return (
    <div
      id="booking-popover"
      className="fixed z-[9999] w-96 bg-white rounded-lg shadow-2xl border border-gray-200"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        maxHeight: 'min(500px, 80vh)',
        overflowY: 'auto',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
      }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <Calendar className="h-5 w-5 text-purple-600" />
          <h3 className="text-lg font-semibold text-gray-900">Nueva Reserva</h3>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-8 w-8 p-0 hover:bg-gray-100"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Content */}
      <form onSubmit={handleSubmit(handleFormSubmit)} className="p-4 space-y-4">
        {/* Date and Time Info */}
        <div className="bg-purple-50 rounded-lg p-3 space-y-2">
          <div className="flex items-center gap-2 text-sm text-purple-700">
            <Calendar className="h-4 w-4" />
            <span className="font-medium">{formattedDate}</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-purple-700">
            <Clock className="h-4 w-4" />
            <span>{watch('startTime')} - {watch('endTime')}</span>
          </div>
        </div>

        {/* Title */}
        <div className="space-y-2">
          <Label htmlFor="title" className="text-sm font-medium text-gray-700">
            Título de la reunión *
          </Label>
          <Input
            id="title"
            {...register('title', { required: 'El título es requerido' })}
            placeholder="Ej: Reunión de equipo"
            className="w-full"
          />
          {errors.title && (
            <p className="text-sm text-red-600">{errors.title.message}</p>
          )}
        </div>

        {/* Meeting Room */}
        <div className="space-y-2">
          <Label htmlFor="meetingRoom" className="text-sm font-medium text-gray-700">
            Sala de reuniones *
          </Label>
          <Select
            value={watchedRoomId || ''}
            onValueChange={(value) => setValue('meetingRoomId', value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Selecciona una sala" />
            </SelectTrigger>
            <SelectContent>
              {meetingRooms.map((room) => (
                <SelectItem key={room.id} value={room.id}>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <span>{room.name}</span>
                    <span className="text-sm text-gray-500">
                      (Cap: {room.capacity})
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.meetingRoomId && (
            <p className="text-sm text-red-600">{errors.meetingRoomId.message}</p>
          )}
        </div>

        {/* Time Inputs */}
        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-2">
            <Label htmlFor="startTime" className="text-sm font-medium text-gray-700">
              Hora inicio
            </Label>
            <Input
              id="startTime"
              type="time"
              {...register('startTime', { required: 'La hora de inicio es requerida' })}
              className="w-full"
            />
            {errors.startTime && (
              <p className="text-sm text-red-600">{errors.startTime.message}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="endTime" className="text-sm font-medium text-gray-700">
              Hora fin
            </Label>
            <Input
              id="endTime"
              type="time"
              {...register('endTime', { required: 'La hora de fin es requerida' })}
              className="w-full"
            />
            {errors.endTime && (
              <p className="text-sm text-red-600">{errors.endTime.message}</p>
            )}
          </div>
        </div>

        {/* Attendees */}
        <div className="space-y-2">
          <Label htmlFor="attendees" className="text-sm font-medium text-gray-700">
            Número de asistentes
          </Label>
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-gray-500" />
            <Input
              id="attendees"
              type="number"
              min="1"
              max={selectedRoom?.capacity || 50}
              {...register('attendees', {
                required: 'El número de asistentes es requerido',
                min: { value: 1, message: 'Debe haber al menos 1 asistente' },
                max: {
                  value: selectedRoom?.capacity || 50,
                  message: `No puede exceder la capacidad de la sala (${selectedRoom?.capacity || 50})`
                }
              })}
              className="flex-1"
            />
          </div>
          {errors.attendees && (
            <p className="text-sm text-red-600">{errors.attendees.message}</p>
          )}
          {selectedRoom && (
            <p className="text-xs text-gray-500">
              Capacidad máxima: {selectedRoom.capacity} personas
            </p>
          )}
        </div>

        {/* Description */}
        <div className="space-y-2">
          <Label htmlFor="description" className="text-sm font-medium text-gray-700">
            Descripción (opcional)
          </Label>
          <Textarea
            id="description"
            {...register('description')}
            placeholder="Describe el propósito de la reunión..."
            className="w-full resize-none"
            rows={2}
          />
        </div>

        {/* Actions */}
        <div className="flex gap-2 pt-2">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            className="flex-1"
            disabled={isLoading}
          >
            Cancelar
          </Button>
          <Button
            type="submit"
            className="flex-1 bg-purple-600 hover:bg-purple-700"
            disabled={isLoading}
          >
            {isLoading ? 'Creando...' : 'Crear Reserva'}
          </Button>
        </div>
      </form>
    </div>
  );
}
