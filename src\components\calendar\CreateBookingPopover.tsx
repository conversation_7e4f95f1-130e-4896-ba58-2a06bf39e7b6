'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { DateTime } from 'luxon';
import { X, Calendar, Clock, Users, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { SelectedSlot, CreateBookingFormData } from './types';

interface CreateBookingPopoverProps {
  isOpen: boolean;
  onClose: () => void;
  selectedSlot: SelectedSlot | null;
  meetingRooms: any[];
  onSubmit: (data: CreateBookingFormData) => Promise<void>;
  isLoading?: boolean;
  position: { x: number; y: number };
}

export function CreateBookingPopover({
  isOpen,
  onClose,
  selectedSlot,
  meetingRooms,
  onSubmit,
  isLoading = false,
  position
}: CreateBookingPopoverProps) {
  const [selectedRoom, setSelectedRoom] = useState<any>(null);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors }
  } = useForm<CreateBookingFormData>();

  const watchedRoomId = watch('meetingRoomId');

  // Update selected room when room ID changes
  useEffect(() => {
    if (watchedRoomId) {
      const room = meetingRooms.find(r => r.id === watchedRoomId);
      setSelectedRoom(room);
    }
  }, [watchedRoomId, meetingRooms]);

  // Set default values when slot is selected
  useEffect(() => {
    if (selectedSlot && isOpen) {
      const startTime = DateTime.fromJSDate(selectedSlot.start).toFormat('HH:mm');
      const endTime = DateTime.fromJSDate(selectedSlot.end).toFormat('HH:mm');

      setValue('startTime', startTime);
      setValue('endTime', endTime);

      // Reset other fields
      setValue('title', '');
      setValue('description', '');
      setValue('attendees', 1);
      setValue('meetingRoomId', '');
      setSelectedRoom(null);
    }
  }, [selectedSlot, isOpen, setValue]);

  // Reset form when popover closes
  useEffect(() => {
    if (!isOpen) {
      reset();
      setSelectedRoom(null);
    }
  }, [isOpen, reset]);

  const handleFormSubmit = async (data: CreateBookingFormData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      // Error handling is done in parent component
    }
  };

  // Close popover when clicking outside
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      const popover = document.getElementById('booking-popover');

      // Don't close if clicking on calendar elements or the popover itself
      if (popover && !popover.contains(target)) {
        // Check if click is on calendar elements
        const isCalendarClick = target.closest('.rbc-calendar') ||
          target.closest('.rbc-event') ||
          target.closest('.rbc-time-slot');

        if (!isCalendarClick) {
          onClose();
        }
      }
    };

    // Add delay to prevent immediate closing when opening
    const timer = setTimeout(() => {
      document.addEventListener('mousedown', handleClickOutside);
    }, 200);

    return () => {
      clearTimeout(timer);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Close on Escape key
  useEffect(() => {
    if (!isOpen) return;

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  // Debug logs
  console.log('CreateBookingPopover render:', {
    isOpen,
    selectedSlot: !!selectedSlot,
    position,
    timestamp: new Date().toISOString()
  });

  if (!isOpen || !selectedSlot) {
    console.log('CreateBookingPopover: Not rendering - isOpen:', isOpen, 'selectedSlot:', !!selectedSlot);
    return null;
  }

  const selectedDate = DateTime.fromJSDate(selectedSlot.start);
  const formattedDate = selectedDate.toFormat('EEEE, dd \'de\' MMMM \'de\' yyyy');

  console.log('CreateBookingPopover: RENDERING POPOVER at position:', position);

  return (
    <div
      id="booking-popover"
      className="fixed z-[9999] w-96 bg-red-500 rounded-lg shadow-2xl border-4 border-blue-500"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        maxHeight: 'min(600px, 80vh)',
        overflowY: 'auto',
        opacity: 1,
        visibility: 'visible',
        pointerEvents: 'auto'
      }}
    >
      {/* Header */}
      <div className="p-4 bg-yellow-300">
        <h3 className="text-lg font-bold text-black">🎉 POPOVER FUNCIONANDO! 🎉</h3>
        <p className="text-black">Posición: {position.x}, {position.y}</p>
        <button
          onClick={onClose}
          className="mt-2 px-4 py-2 bg-blue-600 text-white rounded"
        >
          Cerrar Popover
        </button>
      </div>

      {/* Simplified Content for Testing */}
      <div className="p-4 bg-green-300">
        <p className="text-black font-bold">¡EL POPOVER ESTÁ FUNCIONANDO!</p>
        <p className="text-black">Fecha: {formattedDate}</p>
        <p className="text-black">Hora: {selectedSlot ? 'Slot seleccionado' : 'No slot'}</p>
        <p className="text-black">isMobile detectado en calendario</p>
      </div>
    </div>
  );
}
