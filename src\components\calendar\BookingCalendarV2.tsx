'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import { Calendar, luxonLocalizer, View } from 'react-big-calendar';
import { DateTime, Settings } from 'luxon';
import { toast } from 'sonner';
import { EventModal } from './EventModal';
import { CreateBookingModal } from './CreateBookingModal';
import { CancelBookingDialog } from './CancelBookingDialog';
import { useBookingValidation } from './hooks/useBookingValidation';
import { BookingEvent, SelectedSlot, CreateBookingFormData } from './types';
import { CreateBookingPopover } from './CreateBookingPopover';
import 'react-big-calendar/lib/css/react-big-calendar.css';

// Configure Luxon for Spanish locale
Settings.defaultLocale = 'es';

const localizer = luxonLocalizer(DateTime, {
  firstDayOfWeek: 1 // Monday
});

interface BookingCalendarV2Props {
  events: BookingEvent[];
  meetingRooms: any[];
  onCreateBooking: (data: CreateBookingFormData) => Promise<void>;
  onCancelBooking: (booking: any) => Promise<void>;
  isCreatingBooking?: boolean;
  isCancellingBooking?: boolean;
  className?: string;
}

// Hook to detect screen size
function useMediaQuery(query: string) {
  const [matches, setMatches] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    const media = window.matchMedia(query);
    setMatches(media.matches);

    const listener = () => setMatches(media.matches);
    media.addEventListener('change', listener);
    return () => media.removeEventListener('change', listener);
  }, [query]);

  // Return false during SSR and initial render to prevent hydration mismatch
  return mounted ? matches : false;
}

export function BookingCalendarV2({
  events,
  meetingRooms,
  onCreateBooking,
  onCancelBooking,
  isCreatingBooking = false,
  isCancellingBooking = false,
  className = ''
}: BookingCalendarV2Props) {
  const [currentView, setCurrentView] = useState<View>('week');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedSlot, setSelectedSlot] = useState<SelectedSlot | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<any>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEventModal, setShowEventModal] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [bookingToCancel, setBookingToCancel] = useState<any>(null);
  // State to keep the visual selection visible while modal/popover is open
  const [visibleSelection, setVisibleSelection] = useState<any>(null);
  // Popover state and positioning
  const [showPopover, setShowPopover] = useState(false);
  const [popoverPosition, setPopoverPosition] = useState({ x: 0, y: 0 });
  const calendarRef = useRef<HTMLDivElement>(null);

  // Check if screen is mobile (below 768px)
  const isMobileQuery = useMediaQuery('(max-width: 767px)');

  // Additional check using window.innerWidth as fallback
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      const windowWidth = window.innerWidth;
      const isMobileSize = windowWidth < 768;
      setIsMobile(isMobileSize);

      console.log('Screen size detection:', {
        windowWidth,
        isMobileQuery,
        isMobileSize,
        finalIsMobile: isMobileSize,
        timestamp: new Date().toISOString()
      });
    };

    // Check immediately
    checkScreenSize();

    // Listen for resize events
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, [isMobileQuery]);

  // Create events array with temporary selection event if exists
  const eventsWithSelection = visibleSelection
    ? [...events, {
      start: visibleSelection.start,
      end: visibleSelection.end,
      title: '',
      booking: null,
      id: 'temp-selection',
      isSelection: true
    }]
    : events;

  const { validateBookingRules, validateCapacity } = useBookingValidation();

  // Event style getter
  const eventStyleGetter = useCallback((event: any) => {
    // Handle temporary selection event
    if (event.isSelection) {
      return {
        style: {
          backgroundColor: 'rgba(59, 130, 246, 0.2)', // Light blue background
          borderRadius: '4px',
          border: '2px dashed #3b82f6', // Blue dashed border
          color: 'transparent', // Hide any text
          fontWeight: '500',
          boxShadow: '0 1px 3px rgba(59, 130, 246, 0.3)',
          padding: '4px 8px',
          lineHeight: '1.3',
          pointerEvents: 'none' as const // Prevent interaction with selection event
        }
      };
    }

    const booking = event.booking;
    let borderColor = '#6b7280'; // Default gray

    switch (booking?.status) {
      case 'CONFIRMED':
        borderColor = '#3b82f6'; // Blue
        break;
      case 'COMPLETED':
        borderColor = '#10b981'; // Green
        break;
      case 'CANCELLED':
        borderColor = '#ef4444'; // Red
        break;
      case 'NO_SHOW':
        borderColor = '#6b7280'; // Gray
        break;
    }

    return {
      style: {
        backgroundColor: '#f8f9fa', // Light gray background
        borderRadius: '4px',
        border: 'none',
        borderLeft: `4px solid ${borderColor}`, // Colored left border
        color: '#1f2937', // Dark gray text
        fontWeight: '500',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        padding: '4px 8px',
        lineHeight: '1.3'
        // Remove any positioning styles that interfere with react-big-calendar
      }
    };
  }, []);

  // Calculate optimal popover position based on mouse event or slot position
  const calculatePopoverPosition = useCallback((slotInfo: any, mouseEvent?: MouseEvent) => {
    if (!calendarRef.current) return { x: 0, y: 0 };

    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const popoverWidth = 400; // Estimated popover width
    const popoverHeight = 600; // Estimated popover height
    const margin = 20; // Minimum margin from viewport edges

    let x: number;
    let y: number;

    if (mouseEvent) {
      // Use mouse position if available
      x = mouseEvent.clientX;
      y = mouseEvent.clientY;
    } else {
      // Fallback to calendar-based calculation
      const calendarRect = calendarRef.current.getBoundingClientRect();
      const slotDate = new Date(slotInfo.start);

      // Calculate day position (0 = Sunday, 1 = Monday, etc.)
      const dayOfWeek = slotDate.getDay();
      const adjustedDay = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Convert to Monday = 0

      // Calculate hour position
      const hour = slotDate.getHours();
      const minutes = slotDate.getMinutes();

      // More accurate slot position calculation
      const dayWidth = calendarRect.width / 7;
      const hourHeight = 60; // Approximate height per hour in week view

      x = calendarRect.left + (adjustedDay * dayWidth) + (dayWidth / 2);
      y = calendarRect.top + ((hour - 7) * hourHeight) + (minutes / 60 * hourHeight) + 100; // +100 for header
    }

    // Smart positioning logic
    let finalX = x;
    let finalY = y;

    // Horizontal positioning
    if (x + popoverWidth + margin > viewportWidth) {
      // Not enough space on the right, try left
      finalX = x - popoverWidth - margin;

      // If still not enough space on left, center it
      if (finalX < margin) {
        finalX = Math.max(margin, (viewportWidth - popoverWidth) / 2);
      }
    } else {
      // Enough space on the right
      finalX = x + margin;
    }

    // Vertical positioning
    if (y + popoverHeight + margin > viewportHeight) {
      // Not enough space below, try above
      finalY = y - popoverHeight - margin;

      // If still not enough space above, position at top with scroll
      if (finalY < margin) {
        finalY = margin;
      }
    } else {
      // Enough space below
      finalY = y;
    }

    // Final bounds check
    finalX = Math.max(margin, Math.min(finalX, viewportWidth - popoverWidth - margin));
    finalY = Math.max(margin, Math.min(finalY, viewportHeight - popoverHeight - margin));

    return { x: finalX, y: finalY };
  }, []);

  // Handle slot selection (clicking on empty time slots)
  const handleSelectSlot = useCallback((slotInfo: any) => {
    const now = new Date();
    const slotStart = new Date(slotInfo.start);

    // Don't allow booking in the past
    if (slotStart < now) {
      toast.error('No se pueden crear reservas en el pasado');
      return;
    }

    setSelectedSlot({
      start: slotInfo.start,
      end: slotInfo.end,
      resourceId: slotInfo.resourceId
    });

    // Keep the visual selection visible while modal/popover is open
    // Create a temporary event object for the selection
    setVisibleSelection({
      start: slotInfo.start,
      end: slotInfo.end,
      title: 'Selección',
      resource: slotInfo.resourceId,
      id: 'temp-selection'
    });

    // Debug log
    console.log('handleSelectSlot - Device detection:', {
      isMobile,
      windowWidth: window.innerWidth,
      willUseModal: isMobile,
      willUsePopover: !isMobile
    });

    if (isMobile) {
      // Use modal on mobile
      console.log('Opening modal for mobile');
      setShowCreateModal(true);
    } else {
      // Use popover on desktop
      console.log('Opening popover for desktop');
      const position = calculatePopoverPosition(slotInfo);
      setPopoverPosition(position);
      setShowPopover(true);
    }
  }, [isMobile, calculatePopoverPosition]);

  // Handle event selection (clicking on existing events)
  const handleSelectEvent = useCallback((event: any) => {
    // Ignore clicks on the temporary selection event
    if (event.isSelection) {
      return;
    }

    // Clear any existing slot selection when clicking on an event
    setVisibleSelection(null);
    setSelectedSlot(null);
    setSelectedEvent(event.booking);
    setShowEventModal(true);
  }, []);

  // Handle view change
  const handleViewChange = useCallback((view: View) => {
    setCurrentView(view);
    // Clear selection when changing views
    setVisibleSelection(null);
    setSelectedSlot(null);
    setShowPopover(false);
  }, []);

  // Handle navigation
  const handleNavigate = useCallback((date: Date) => {
    setCurrentDate(date);
    // Clear selection when navigating to different dates
    setVisibleSelection(null);
    setSelectedSlot(null);
    setShowPopover(false);
  }, []);

  // Handle booking creation
  const handleCreateBooking = async (data: CreateBookingFormData) => {
    try {
      if (!selectedSlot) {
        throw new Error('No se ha seleccionado un horario');
      }

      // Validate booking rules
      const rulesValidation = validateBookingRules(data.startTime, data.endTime, selectedSlot.start);
      if (!rulesValidation.isValid) {
        toast.error(rulesValidation.error);
        return;
      }

      // Validate capacity
      const capacityValidation = validateCapacity();
      if (!capacityValidation.isValid) {
        toast.error(capacityValidation.error);
        return;
      }

      // Pass the selected date to the parent handler
      const dataWithDate = { ...data, selectedDate: selectedSlot.start };
      await onCreateBooking(dataWithDate);

      // Close modal/popover and clear selection
      setShowCreateModal(false);
      setShowPopover(false);
      setSelectedSlot(null);
      setVisibleSelection(null); // Clear visual selection when booking is created
      toast.success('Reserva creada exitosamente');
    } catch (error: any) {
      toast.error(error.message || 'Error al crear la reserva');
    }
  };

  // Handle booking cancellation
  const handleCancelBooking = (booking: any) => {
    setBookingToCancel(booking);
    setShowCancelDialog(true);
  };

  const confirmCancelBooking = async () => {
    if (!bookingToCancel) return;

    try {
      await onCancelBooking(bookingToCancel);
      setShowCancelDialog(false);
      setShowEventModal(false);
      setBookingToCancel(null);
      toast.success('Reserva cancelada exitosamente');
    } catch (error: any) {
      toast.error(error.message || 'Error al cancelar la reserva');
    }
  };

  // Check if booking can be cancelled
  const canCancelBooking = (booking: any) => {
    if (!booking) return false;
    const now = new Date();
    const bookingStart = new Date(booking.startDate);
    return booking.status === 'CONFIRMED' && bookingStart > now;
  };

  // Close popover/modal handlers
  const handleCloseCreate = () => {
    setShowCreateModal(false);
    setShowPopover(false);
    setSelectedSlot(null);
    setVisibleSelection(null); // Clear visual selection when modal/popover closes
  };

  // Calendar messages in Spanish
  const messages = {
    allDay: 'Todo el día',
    previous: 'Anterior',
    next: 'Siguiente',
    today: 'Hoy',
    month: 'Mes',
    week: 'Semana',
    day: 'Día',
    agenda: 'Agenda',
    date: 'Fecha',
    time: 'Hora',
    event: 'Evento',
    noEventsInRange: 'No hay eventos en este rango',
    showMore: (total: number) => `+ ${total} más`
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            📅 Calendario de Reservas V2
          </h2>
          <p className="text-gray-600 text-sm mt-1">
            Haz clic en un evento para ver detalles o en un espacio vacío para crear una nueva reserva
            {isMobile ? ' (Modal)' : ' (Popover)'}
          </p>
        </div>

        <div className="h-[600px]" ref={calendarRef}>
          <Calendar
            localizer={localizer}
            events={eventsWithSelection}
            startAccessor="start"
            endAccessor="end"
            titleAccessor="title"
            view={currentView}
            onView={handleViewChange}
            date={currentDate}
            onNavigate={handleNavigate}
            onSelectSlot={handleSelectSlot}
            onSelectEvent={handleSelectEvent}
            selectable
            popup
            eventPropGetter={eventStyleGetter}
            messages={messages}
            formats={{
              timeGutterFormat: 'HH:mm',
              eventTimeRangeFormat: ({ start, end }) => {
                const startTime = DateTime.fromJSDate(start).toFormat('HH:mm');
                const endTime = DateTime.fromJSDate(end).toFormat('HH:mm');
                return `${startTime} - ${endTime}`;
              },
              agendaTimeRangeFormat: ({ start, end }) => {
                const startTime = DateTime.fromJSDate(start).toFormat('HH:mm');
                const endTime = DateTime.fromJSDate(end).toFormat('HH:mm');
                return `${startTime} - ${endTime}`;
              }
            }}
            min={new Date(2024, 0, 1, 7, 0)} // 7:00 AM
            max={new Date(2024, 0, 1, 22, 0)} // 10:00 PM
            step={15}
            timeslots={4}
          />
        </div>
      </div>

      {/* Mobile Modal */}
      <CreateBookingModal
        isOpen={showCreateModal}
        onClose={handleCloseCreate}
        selectedSlot={selectedSlot}
        meetingRooms={meetingRooms}
        onSubmit={handleCreateBooking}
        isLoading={isCreatingBooking}
      />

      {/* Desktop Popover */}
      <CreateBookingPopover
        isOpen={showPopover}
        onClose={handleCloseCreate}
        selectedSlot={selectedSlot}
        meetingRooms={meetingRooms}
        onSubmit={handleCreateBooking}
        isLoading={isCreatingBooking}
        position={popoverPosition}
      />

      <EventModal
        isOpen={showEventModal}
        onClose={() => setShowEventModal(false)}
        booking={selectedEvent}
        onCancel={handleCancelBooking}
        canCancel={canCancelBooking(selectedEvent)}
      />

      <CancelBookingDialog
        isOpen={showCancelDialog}
        onClose={() => {
          setShowCancelDialog(false);
          setBookingToCancel(null);
        }}
        onConfirm={confirmCancelBooking}
        booking={bookingToCancel}
        isLoading={isCancellingBooking}
      />
    </div>
  );
}
